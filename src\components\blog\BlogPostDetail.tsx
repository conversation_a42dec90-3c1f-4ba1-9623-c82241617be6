"use client";

import { useState } from "react";
import {
  Box,
  Heading,
  Text,
  Flex,
  Avatar,
  Button,
  IconButton,
  Badge,
  Textarea,
  useToast,
  useColorModeValue,
  Link,
  Container,
  HStack,
  Tag,
  Image,
} from "@chakra-ui/react";
import { useTranslation } from "react-i18next";
import { useRouter } from "next/navigation";
import NextLink from "next/link";
import { formatDistanceToNow } from "date-fns";
import {
  Heart,
  MessageSquare,
  Share2,
  MoreVertical,
  Edit,
  Trash,
  Flag,
  ArrowLeft,
  Send,
  Facebook,
  Twitter,
  Linkedin,
  Copy,
  User,
} from "lucide-react";
import { supabase } from "@/lib/supabase/client";
import { useDirection } from "@/lib/contexts/DirectionContext";
import { Divider } from "@/components/ui/separator";
import { MenuRoot, MenuTrigger, MenuContent, MenuItem } from "@/components/ui/menu";

interface BlogPostDetailProps {
  post: any;
  comments: any[];
  reactions: any[];
  currentUser: any;
  userReaction: any;
  relatedPosts?: any[];
}

export default function BlogPostDetail({
  post,
  comments: initialComments,
  reactions: initialReactions,
  currentUser,
  userReaction: initialUserReaction,
  relatedPosts = [],
}: BlogPostDetailProps) {
  const { t } = useTranslation();
  const router = useRouter();
  const { direction } = useDirection();
  const toast = useToast();

  const [comments, setComments] = useState(initialComments);
  const [reactions, setReactions] = useState(initialReactions);
  const [userReaction, setUserReaction] = useState(initialUserReaction);
  const [newComment, setNewComment] = useState("");
  const [isSubmittingComment, setIsSubmittingComment] = useState(false);
  const [showShareOptions, setShowShareOptions] = useState(false);

  const bgColor = useColorModeValue("white", "gray.800");
  const borderColor = useColorModeValue("gray.200", "gray.700");

  const handleDeletePost = async () => {
    if (
      window.confirm(
        t(
          "blog.confirmDelete",
          "Are you sure you want to delete this blog post?",
        ),
      )
    ) {
      const { error } = await supabase
        .from("content_modules")
        .delete()
        .eq("id", post.id);

      if (!error) {
        toast({
          title: t("blog.deleteSuccess", "Blog post deleted"),
          status: "success",
          duration: 3000,
          isClosable: true,
        });
        router.push(`/spaces/${post.space_id}`);
        router.refresh();
      } else {
        toast({
          title: t("blog.deleteError", "Error deleting blog post"),
          description: error.message,
          status: "error",
          duration: 5000,
          isClosable: true,
        });
      }
    }
  };

  const handleToggleReaction = async () => {
    if (userReaction) {
      // Remove reaction
      const { error } = await supabase
        .from("content_reactions")
        .delete()
        .eq("id", userReaction.id);

      if (!error) {
        setReactions(reactions.filter((r) => r.id !== userReaction.id));
        setUserReaction(null);
      }
    } else {
      // Add reaction
      const { data, error } = await supabase
        .from("content_reactions")
        .insert([
          {
            content_id: post.id,
            profile_id: currentUser.id,
            reaction_type: "like",
          },
        ])
        .select();

      if (!error && data) {
        setReactions([...reactions, data[0]]);
        setUserReaction(data[0]);
      }
    }
  };

  const handleSubmitComment = async () => {
    if (!newComment.trim()) return;

    setIsSubmittingComment(true);

    try {
      const { data, error } = await supabase
        .from("content_comments")
        .insert([
          {
            content_id: post.id,
            author_id: currentUser.id,
            comment_text: newComment,
          },
        ])
        .select(`*, author:profiles(*)`);

      if (error) throw error;

      if (data) {
        setComments([...comments, data[0]]);
        setNewComment("");
      }
    } catch (error: any) {
      toast({
        title: t("comment.submitError", "Error submitting comment"),
        description: error.message,
        status: "error",
        duration: 3000,
        isClosable: true,
      });
    } finally {
      setIsSubmittingComment(false);
    }
  };

  const handleDeleteComment = async (commentId: string) => {
    if (
      window.confirm(
        t(
          "comment.confirmDelete",
          "Are you sure you want to delete this comment?",
        ),
      )
    ) {
      const { error } = await supabase
        .from("content_comments")
        .delete()
        .eq("id", commentId);

      if (!error) {
        setComments(comments.filter((c) => c.id !== commentId));
        toast({
          title: t("comment.deleteSuccess", "Comment deleted"),
          status: "success",
          duration: 3000,
          isClosable: true,
        });
      } else {
        toast({
          title: t("comment.deleteError", "Error deleting comment"),
          description: error.message,
          status: "error",
          duration: 3000,
          isClosable: true,
        });
      }
    }
  };

  const handleShare = (platform: string) => {
    const url = window.location.href;
    let shareUrl = "";

    switch (platform) {
      case "facebook":
        shareUrl = `https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(url)}`;
        break;
      case "twitter":
        shareUrl = `https://twitter.com/intent/tweet?url=${encodeURIComponent(url)}&text=${encodeURIComponent(post.title)}`;
        break;
      case "linkedin":
        shareUrl = `https://www.linkedin.com/sharing/share-offsite/?url=${encodeURIComponent(url)}`;
        break;
      case "copy":
        navigator.clipboard.writeText(url);
        toast({
          title: t("blog.linkCopied", "Link copied to clipboard"),
          status: "success",
          duration: 2000,
          isClosable: true,
        });
        return;
    }

    if (shareUrl) {
      window.open(shareUrl, "_blank", "noopener,noreferrer");
    }
  };

  // Extract metadata
  const tags = post.metadata?.tags || [];
  const category = post.metadata?.category;
  const coverImage = post.content?.image || post.metadata?.coverImage;

  return (
    <Box dir={direction}>
      {/* Back Button */}
      <Container maxW="container.xl" py={6}>
        <Button
          leftIcon={<ArrowLeft size={16} />}
          variant="ghost"
          mb={6}
          onClick={() => router.push(`/spaces/${post.space_id}`)}
        >
          {t("common.back", "Back")}
        </Button>

        {/* Post Content */}
        <Box
          bg={bgColor}
          borderRadius="xl"
          borderWidth="1px"
          borderColor={borderColor}
          overflow="hidden"
          mb={6}
        >
          {/* Cover Image */}
          {coverImage && (
            <Box height="400px" overflow="hidden">
              <Image
                src={coverImage}
                alt={post.title}
                objectFit="cover"
                width="100%"
                height="100%"
              />
            </Box>
          )}

          <Box p={8}>
            {/* Category & Options */}
            <Flex justify="space-between" align="center" mb={4}>
              {category && (
                <Badge
                  colorScheme="teal"
                  fontSize="sm"
                  px={3}
                  py={1}
                  borderRadius="full"
                >
                  {category}
                </Badge>
              )}

              <MenuRoot>
                <MenuTrigger asChild>
                  <IconButton
                    aria-label="Options"
                    variant="ghost"
                  >
                    <MoreVertical size={16} />
                  </IconButton>
                </MenuTrigger>
                <MenuContent>
                  {currentUser?.id === post.author_id ? (
                    <>
                      <MenuItem
                        onClick={() =>
                          router.push(
                            `/spaces/${post.space_id}/posts/${post.id}/edit`,
                          )
                        }
                      >
                        <Edit size={16} />
                        {t("blog.edit", "Edit post")}
                      </MenuItem>
                      <MenuItem
                        onClick={handleDeletePost}
                      >
                        <Trash size={16} />
                        {t("blog.delete", "Delete post")}
                      </MenuItem>
                    </>
                  ) : (
                    <MenuItem>
                      <Flag size={16} />
                      {t("blog.report", "Report post")}
                    </MenuItem>
                  )}
                </MenuContent>
              </MenuRoot>
            </Flex>

            {/* Title */}
            <Heading as="h1" size="2xl" mb={6}>
              {post.title}
            </Heading>

            {/* Author Info */}
            <Flex align="center" gap={3} mb={8}>
              <Avatar
                size="md"
                name={post.author?.full_name}
                src={post.author?.avatar_url}
              />
              <Box>
                <Link
                  as={NextLink}
                  href={`/author/${post.author_id}`}
                  fontWeight="medium"
                  _hover={{ textDecoration: "underline" }}
                >
                  {post.author?.full_name}
                </Link>
                <Flex align="center" gap={2}>
                  <Text color="gray.500" fontSize="sm">
                    {formatDistanceToNow(new Date(post.created_at), {
                      addSuffix: true,
                    })}
                  </Text>
                  {post.status === "featured" && (
                    <Badge colorScheme="purple">
                      {t("blog.featured", "Featured")}
                    </Badge>
                  )}
                </Flex>
              </Box>
            </Flex>

            {/* Tags */}
            {tags.length > 0 && (
              <HStack spacing={2} mb={8} flexWrap="wrap">
                {tags.map((tag: string, index: number) => (
                  <Tag
                    key={index}
                    size="md"
                    borderRadius="full"
                    variant="subtle"
                    colorScheme="teal"
                  >
                    {tag}
                  </Tag>
                ))}
              </HStack>
            )}

            {/* Content */}
            <Text whiteSpace="pre-wrap" mb={8} fontSize="lg" lineHeight="1.8">
              {typeof post.content === "string"
                ? post.content
                : post.content.text || ""}
            </Text>

            <Divider mb={6} />

            {/* Engagement Actions */}
            <Flex align="center" gap={4} mb={2}>
              <Button
                leftIcon={<Heart size={16} />}
                variant={userReaction ? "solid" : "ghost"}
                colorScheme={userReaction ? "red" : "gray"}
                size="sm"
                onClick={handleToggleReaction}
              >
                {reactions.length} {t("blog.likes", "Likes")}
              </Button>
              <Button
                leftIcon={<MessageSquare size={16} />}
                variant="ghost"
                size="sm"
                onClick={() =>
                  document
                    .getElementById("comments-section")
                    ?.scrollIntoView({ behavior: "smooth" })
                }
              >
                {comments.length} {t("blog.comments", "Comments")}
              </Button>
              <Button
                leftIcon={<Share2 size={16} />}
                variant="ghost"
                size="sm"
                onClick={() => setShowShareOptions(!showShareOptions)}
              >
                {t("blog.share", "Share")}
              </Button>
            </Flex>

            {/* Share Options */}
            {showShareOptions && (
              <Flex mt={4} gap={2}>
                <IconButton
                  aria-label="Share on Facebook"
                  icon={<Facebook size={18} />}
                  colorScheme="facebook"
                  variant="outline"
                  size="sm"
                  onClick={() => handleShare("facebook")}
                />
                <IconButton
                  aria-label="Share on Twitter"
                  icon={<Twitter size={18} />}
                  colorScheme="twitter"
                  variant="outline"
                  size="sm"
                  onClick={() => handleShare("twitter")}
                />
                <IconButton
                  aria-label="Share on LinkedIn"
                  icon={<Linkedin size={18} />}
                  colorScheme="linkedin"
                  variant="outline"
                  size="sm"
                  onClick={() => handleShare("linkedin")}
                />
                <IconButton
                  aria-label="Copy Link"
                  icon={<Copy size={18} />}
                  variant="outline"
                  size="sm"
                  onClick={() => handleShare("copy")}
                />
              </Flex>
            )}
          </Box>
        </Box>

        {/* Author Bio Section */}
        <Box
          bg={bgColor}
          borderRadius="xl"
          borderWidth="1px"
          borderColor={borderColor}
          p={6}
          mb={6}
        >
          <Flex gap={4} align="center">
            <Avatar
              size="xl"
              name={post.author?.full_name}
              src={post.author?.avatar_url}
            />
            <Box>
              <Heading as="h3" size="md" mb={2}>
                <Link
                  as={NextLink}
                  href={`/author/${post.author_id}`}
                  _hover={{ textDecoration: "underline" }}
                >
                  {post.author?.full_name}
                </Link>
              </Heading>
              <Text color="gray.600" mb={3}>
                {post.author?.bio ||
                  t("blog.noBio", "This author has not added a bio yet.")}
              </Text>
              <Button
                leftIcon={<User size={16} />}
                size="sm"
                as={NextLink}
                href={`/author/${post.author_id}`}
              >
                {t("blog.viewProfile", "View Profile")}
              </Button>
            </Box>
          </Flex>
        </Box>

        {/* Related Posts Section */}
        {relatedPosts.length > 0 && (
          <Box mb={8}>
            <Heading as="h3" size="lg" mb={4}>
              {t("blog.relatedPosts", "Related Posts")}
            </Heading>
            <Flex gap={6} flexWrap="wrap">
              {relatedPosts.map((relatedPost) => (
                <Box
                  key={relatedPost.id}
                  width={{ base: "100%", md: "calc(33.33% - 16px)" }}
                  bg={bgColor}
                  borderRadius="xl"
                  borderWidth="1px"
                  borderColor={borderColor}
                  overflow="hidden"
                >
                  {relatedPost.content?.image && (
                    <Box height="200px" overflow="hidden">
                      <Image
                        src={relatedPost.content.image}
                        alt={relatedPost.title}
                        objectFit="cover"
                        width="100%"
                        height="100%"
                      />
                    </Box>
                  )}
                  <Box p={4}>
                    <Heading as="h4" size="md" mb={2} noOfLines={2}>
                      <Link
                        as={NextLink}
                        href={`/spaces/${relatedPost.space_id}/posts/${relatedPost.id}`}
                        _hover={{ textDecoration: "underline" }}
                      >
                        {relatedPost.title}
                      </Link>
                    </Heading>
                    <Flex align="center" gap={2} mb={2}>
                      <Avatar
                        size="xs"
                        name={relatedPost.author?.full_name}
                        src={relatedPost.author?.avatar_url}
                      />
                      <Text fontSize="sm">{relatedPost.author?.full_name}</Text>
                    </Flex>
                    <Text noOfLines={2} fontSize="sm" color="gray.600">
                      {typeof relatedPost.content === "string"
                        ? relatedPost.content
                        : relatedPost.content.text || ""}
                    </Text>
                  </Box>
                </Box>
              ))}
            </Flex>
          </Box>
        )}

        {/* Comments Section */}
        <Box
          id="comments-section"
          bg={bgColor}
          borderRadius="xl"
          borderWidth="1px"
          borderColor={borderColor}
          p={6}
        >
          <Heading as="h2" size="lg" mb={6}>
            {t("blog.comments", "Comments")} ({comments.length})
          </Heading>

          {/* Add Comment */}
          {currentUser && (
            <Flex gap={4} mb={8}>
              <Avatar
                size="md"
                name={currentUser?.full_name}
                src={currentUser?.avatar_url}
              />
              <Box flex={1}>
                <Textarea
                  placeholder={t("comment.placeholder", "Write a comment...")}
                  value={newComment}
                  onChange={(e) => setNewComment(e.target.value)}
                  mb={2}
                />
                <Flex justify="flex-end">
                  <Button
                    rightIcon={<Send size={16} />}
                    colorScheme="teal"
                    isDisabled={!newComment.trim()}
                    isLoading={isSubmittingComment}
                    onClick={handleSubmitComment}
                  >
                    {t("comment.submit", "Post Comment")}
                  </Button>
                </Flex>
              </Box>
            </Flex>
          )}

          {/* Comments List */}
          {comments.length > 0 ? (
            <Box>
              {comments.map((comment) => (
                <Box key={comment.id} mb={6}>
                  <Flex gap={4}>
                    <Avatar
                      size="sm"
                      name={comment.author?.full_name}
                      src={comment.author?.avatar_url}
                    />
                    <Box
                      flex={1}
                      p={4}
                      borderRadius="md"
                      bg={useColorModeValue("gray.50", "gray.700")}
                    >
                      <Flex justify="space-between" align="flex-start">
                        <Box>
                          <Text fontWeight="medium">
                            {comment.author?.full_name}
                          </Text>
                          <Text fontSize="xs" color="gray.500" mb={2}>
                            {formatDistanceToNow(new Date(comment.created_at), {
                              addSuffix: true,
                            })}
                          </Text>
                        </Box>

                        {(currentUser?.id === comment.author_id ||
                          currentUser?.id === post.author_id) && (
                          <Menu>
                            <MenuButton
                              as={IconButton}
                              icon={<MoreVertical size={14} />}
                              variant="ghost"
                              aria-label="Comment options"
                              size="xs"
                            />
                            <MenuList>
                              {currentUser?.id === comment.author_id && (
                                <MenuItem
                                  icon={<Edit size={14} />}
                                  fontSize="sm"
                                >
                                  {t("comment.edit", "Edit")}
                                </MenuItem>
                              )}
                              <MenuItem
                                icon={<Trash size={14} />}
                                fontSize="sm"
                                onClick={() => handleDeleteComment(comment.id)}
                              >
                                {t("comment.delete", "Delete")}
                              </MenuItem>
                            </MenuList>
                          </Menu>
                        )}
                      </Flex>

                      <Text>{comment.comment_text}</Text>
                    </Box>
                  </Flex>
                </Box>
              ))}
            </Box>
          ) : (
            <Box textAlign="center" py={8}>
              <Text color="gray.500">
                {t(
                  "comment.noComments",
                  "No comments yet. Be the first to comment!",
                )}
              </Text>
            </Box>
          )}
        </Box>
      </Container>
    </Box>
  );
}
