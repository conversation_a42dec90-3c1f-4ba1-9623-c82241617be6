[08:33:19.951] Running build in Washington, D.C., USA (East) – iad1
[08:33:19.952] Build machine configuration: 2 cores, 8 GB
[08:33:19.986] Cloning github.com/meto002/123 (Branch: main, Commit: 9001a9d)
[08:33:20.161] Previous build caches not available
[08:33:20.419] Cloning completed: 432.000ms
[08:33:20.725] Running "vercel build"
[08:33:21.363] Vercel CLI 42.1.1
[08:33:21.651] Installing dependencies...
[08:33:25.931] npm warn deprecated inflight@1.0.6: This module is not supported, and leaks memory. Do not use it. Check out lru-cache if you want a good and tested way to coalesce async requests by a key value, which is much more comprehensive and powerful.
[08:33:26.471] npm warn deprecated domexception@4.0.0: Use your platform's native DOMException instead
[08:33:26.735] npm warn deprecated abab@2.0.6: Use your platform's native atob() and btoa() methods instead
[08:33:28.257] npm warn deprecated glob@7.2.3: Glob versions prior to v9 are no longer supported
[08:33:28.456] npm warn deprecated glob@7.2.3: Glob versions prior to v9 are no longer supported
[08:33:28.488] npm warn deprecated glob@7.2.3: Glob versions prior to v9 are no longer supported
[08:33:28.617] npm warn deprecated glob@7.2.3: Glob versions prior to v9 are no longer supported
[08:33:29.010] npm warn deprecated glob@7.2.3: Glob versions prior to v9 are no longer supported
[08:33:38.855] 
[08:33:38.855] added 952 packages in 17s
[08:33:38.856] 
[08:33:38.856] 124 packages are looking for funding
[08:33:38.856]   run `npm fund` for details
[08:33:39.117] Detected Next.js version: 14.2.23
[08:33:39.125] Running "npm run build"
[08:33:39.248] 
[08:33:39.248] > build
[08:33:39.248] > next build
[08:33:39.249] 
[08:33:40.428] Attention: Next.js now collects completely anonymous telemetry regarding usage.
[08:33:40.432] This information is used to shape Next.js' roadmap and prioritize features.
[08:33:40.433] You can learn more, including how to opt-out if you'd not like to participate in this anonymous program, by visiting the following URL:
[08:33:40.433] https://nextjs.org/telemetry
[08:33:40.433] 
[08:33:40.558]   ▲ Next.js 14.2.23
[08:33:40.559] 
[08:33:40.798]    Creating an optimized production build ...
[08:34:03.134] <w> [webpack.cache.PackFileCacheStrategy] Serializing big strings (153kiB) impacts deserialization performance (consider using Buffer instead and decode when needed)
[08:34:03.135] <w> [webpack.cache.PackFileCacheStrategy] Serializing big strings (153kiB) impacts deserialization performance (consider using Buffer instead and decode when needed)
[08:34:03.136] <w> [webpack.cache.PackFileCacheStrategy] Serializing big strings (153kiB) impacts deserialization performance (consider using Buffer instead and decode when needed)
[08:34:03.136] <w> [webpack.cache.PackFileCacheStrategy] Serializing big strings (153kiB) impacts deserialization performance (consider using Buffer instead and decode when needed)
[08:34:03.136] <w> [webpack.cache.PackFileCacheStrategy] Serializing big strings (153kiB) impacts deserialization performance (consider using Buffer instead and decode when needed)
[08:34:03.136] <w> [webpack.cache.PackFileCacheStrategy] Serializing big strings (153kiB) impacts deserialization performance (consider using Buffer instead and decode when needed)
[08:34:03.137] <w> [webpack.cache.PackFileCacheStrategy] Serializing big strings (153kiB) impacts deserialization performance (consider using Buffer instead and decode when needed)
[08:34:03.137] <w> [webpack.cache.PackFileCacheStrategy] Serializing big strings (153kiB) impacts deserialization performance (consider using Buffer instead and decode when needed)
[08:34:03.137] <w> [webpack.cache.PackFileCacheStrategy] Serializing big strings (152kiB) impacts deserialization performance (consider using Buffer instead and decode when needed)
[08:34:03.137] <w> [webpack.cache.PackFileCacheStrategy] Serializing big strings (153kiB) impacts deserialization performance (consider using Buffer instead and decode when needed)
[08:34:03.138] <w> [webpack.cache.PackFileCacheStrategy] Serializing big strings (152kiB) impacts deserialization performance (consider using Buffer instead and decode when needed)
[08:34:03.138] <w> [webpack.cache.PackFileCacheStrategy] Serializing big strings (152kiB) impacts deserialization performance (consider using Buffer instead and decode when needed)
[08:34:03.142] <w> [webpack.cache.PackFileCacheStrategy] Serializing big strings (153kiB) impacts deserialization performance (consider using Buffer instead and decode when needed)
[08:34:03.143] <w> [webpack.cache.PackFileCacheStrategy] Serializing big strings (153kiB) impacts deserialization performance (consider using Buffer instead and decode when needed)
[08:34:03.144] <w> [webpack.cache.PackFileCacheStrategy] Serializing big strings (153kiB) impacts deserialization performance (consider using Buffer instead and decode when needed)
[08:34:03.144] <w> [webpack.cache.PackFileCacheStrategy] Serializing big strings (153kiB) impacts deserialization performance (consider using Buffer instead and decode when needed)
[08:34:03.253] <w> [webpack.cache.PackFileCacheStrategy] Serializing big strings (153kiB) impacts deserialization performance (consider using Buffer instead and decode when needed)
[08:34:03.253] <w> [webpack.cache.PackFileCacheStrategy] Serializing big strings (153kiB) impacts deserialization performance (consider using Buffer instead and decode when needed)
[08:34:03.254] <w> [webpack.cache.PackFileCacheStrategy] Serializing big strings (153kiB) impacts deserialization performance (consider using Buffer instead and decode when needed)
[08:34:03.254] <w> [webpack.cache.PackFileCacheStrategy] Serializing big strings (153kiB) impacts deserialization performance (consider using Buffer instead and decode when needed)
[08:34:03.254] <w> [webpack.cache.PackFileCacheStrategy] Serializing big strings (153kiB) impacts deserialization performance (consider using Buffer instead and decode when needed)
[08:34:03.254] <w> [webpack.cache.PackFileCacheStrategy] Serializing big strings (153kiB) impacts deserialization performance (consider using Buffer instead and decode when needed)
[08:34:03.255] <w> [webpack.cache.PackFileCacheStrategy] Serializing big strings (153kiB) impacts deserialization performance (consider using Buffer instead and decode when needed)
[08:34:03.255] <w> [webpack.cache.PackFileCacheStrategy] Serializing big strings (153kiB) impacts deserialization performance (consider using Buffer instead and decode when needed)
[08:34:03.255] <w> [webpack.cache.PackFileCacheStrategy] Serializing big strings (152kiB) impacts deserialization performance (consider using Buffer instead and decode when needed)
[08:34:03.255] <w> [webpack.cache.PackFileCacheStrategy] Serializing big strings (153kiB) impacts deserialization performance (consider using Buffer instead and decode when needed)
[08:34:03.255] <w> [webpack.cache.PackFileCacheStrategy] Serializing big strings (152kiB) impacts deserialization performance (consider using Buffer instead and decode when needed)
[08:34:03.256] <w> [webpack.cache.PackFileCacheStrategy] Serializing big strings (152kiB) impacts deserialization performance (consider using Buffer instead and decode when needed)
[08:34:03.265] <w> [webpack.cache.PackFileCacheStrategy] Serializing big strings (153kiB) impacts deserialization performance (consider using Buffer instead and decode when needed)
[08:34:03.266] <w> [webpack.cache.PackFileCacheStrategy] Serializing big strings (153kiB) impacts deserialization performance (consider using Buffer instead and decode when needed)
[08:34:03.267] <w> [webpack.cache.PackFileCacheStrategy] Serializing big strings (153kiB) impacts deserialization performance (consider using Buffer instead and decode when needed)
[08:34:03.267] <w> [webpack.cache.PackFileCacheStrategy] Serializing big strings (153kiB) impacts deserialization performance (consider using Buffer instead and decode when needed)
[08:34:26.610] <w> [webpack.cache.PackFileCacheStrategy] Serializing big strings (153kiB) impacts deserialization performance (consider using Buffer instead and decode when needed)
[08:34:26.611] <w> [webpack.cache.PackFileCacheStrategy] Serializing big strings (153kiB) impacts deserialization performance (consider using Buffer instead and decode when needed)
[08:34:26.611] <w> [webpack.cache.PackFileCacheStrategy] Serializing big strings (153kiB) impacts deserialization performance (consider using Buffer instead and decode when needed)
[08:34:26.611] <w> [webpack.cache.PackFileCacheStrategy] Serializing big strings (153kiB) impacts deserialization performance (consider using Buffer instead and decode when needed)
[08:34:26.635] <w> [webpack.cache.PackFileCacheStrategy] Serializing big strings (153kiB) impacts deserialization performance (consider using Buffer instead and decode when needed)
[08:34:26.635] <w> [webpack.cache.PackFileCacheStrategy] Serializing big strings (153kiB) impacts deserialization performance (consider using Buffer instead and decode when needed)
[08:34:26.636] <w> [webpack.cache.PackFileCacheStrategy] Serializing big strings (153kiB) impacts deserialization performance (consider using Buffer instead and decode when needed)
[08:34:26.636] <w> [webpack.cache.PackFileCacheStrategy] Serializing big strings (153kiB) impacts deserialization performance (consider using Buffer instead and decode when needed)
[08:34:26.710] <w> [webpack.cache.PackFileCacheStrategy] Serializing big strings (153kiB) impacts deserialization performance (consider using Buffer instead and decode when needed)
[08:34:26.710] <w> [webpack.cache.PackFileCacheStrategy] Serializing big strings (153kiB) impacts deserialization performance (consider using Buffer instead and decode when needed)
[08:34:26.710] <w> [webpack.cache.PackFileCacheStrategy] Serializing big strings (153kiB) impacts deserialization performance (consider using Buffer instead and decode when needed)
[08:34:26.711] <w> [webpack.cache.PackFileCacheStrategy] Serializing big strings (153kiB) impacts deserialization performance (consider using Buffer instead and decode when needed)
[08:34:26.723] <w> [webpack.cache.PackFileCacheStrategy] Serializing big strings (153kiB) impacts deserialization performance (consider using Buffer instead and decode when needed)
[08:34:26.725] <w> [webpack.cache.PackFileCacheStrategy] Serializing big strings (153kiB) impacts deserialization performance (consider using Buffer instead and decode when needed)
[08:34:26.725] <w> [webpack.cache.PackFileCacheStrategy] Serializing big strings (153kiB) impacts deserialization performance (consider using Buffer instead and decode when needed)
[08:34:26.725] <w> [webpack.cache.PackFileCacheStrategy] Serializing big strings (153kiB) impacts deserialization performance (consider using Buffer instead and decode when needed)
[08:34:28.791]  ✓ Compiled successfully
[08:34:28.792]    Linting and checking validity of types ...
[08:34:42.186] Failed to compile.
[08:34:42.186] 
[08:34:42.187] ./src/components/blog/BlogPostDetail.tsx:14:3
[08:34:42.187] Type error: '"@chakra-ui/react"' has no exported member named 'useToast'. Did you mean 'useTabs'?
[08:34:42.187] 
[08:34:42.187] [0m [90m 12 |[39m   [33mBadge[39m[33m,[39m[0m
[08:34:42.187] [0m [90m 13 |[39m   [33mTextarea[39m[33m,[39m[0m
[08:34:42.187] [0m[31m[1m>[22m[39m[90m 14 |[39m   useToast[33m,[39m[0m
[08:34:42.187] [0m [90m    |[39m   [31m[1m^[22m[39m[0m
[08:34:42.187] [0m [90m 15 |[39m   useColorModeValue[33m,[39m[0m
[08:34:42.187] [0m [90m 16 |[39m   [33mLink[39m[33m,[39m[0m
[08:34:42.187] [0m [90m 17 |[39m   [33mContainer[39m[33m,[39m[0m
[08:34:42.223] Static worker exited with code: 1 and signal: null
[08:34:42.244] Error: Command "npm run build" exited with 1
[08:34:42.572] 
[08:34:45.689] Exiting build container