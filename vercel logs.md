[07:30:04.214] Running build in Washington, D.C., USA (East) – iad1
[07:30:04.214] Build machine configuration: 2 cores, 8 GB
[07:30:04.231] Cloning github.com/meto002/123 (Branch: main, Commit: db1fb84)
[07:30:04.407] Previous build caches not available
[07:30:04.671] Cloning completed: 439.000ms
[07:30:04.967] Running "vercel build"
[07:30:05.403] Vercel CLI 42.1.1
[07:30:05.703] Installing dependencies...
[07:30:10.265] npm warn deprecated inflight@1.0.6: This module is not supported, and leaks memory. Do not use it. Check out lru-cache if you want a good and tested way to coalesce async requests by a key value, which is much more comprehensive and powerful.
[07:30:10.938] npm warn deprecated domexception@4.0.0: Use your platform's native DOMException instead
[07:30:11.200] npm warn deprecated abab@2.0.6: Use your platform's native atob() and btoa() methods instead
[07:30:12.879] npm warn deprecated glob@7.2.3: Glob versions prior to v9 are no longer supported
[07:30:13.002] npm warn deprecated glob@7.2.3: Glob versions prior to v9 are no longer supported
[07:30:13.062] npm warn deprecated glob@7.2.3: Glob versions prior to v9 are no longer supported
[07:30:13.137] npm warn deprecated glob@7.2.3: Glob versions prior to v9 are no longer supported
[07:30:13.528] npm warn deprecated glob@7.2.3: Glob versions prior to v9 are no longer supported
[07:30:23.831] 
[07:30:23.831] added 952 packages in 18s
[07:30:23.832] 
[07:30:23.832] 124 packages are looking for funding
[07:30:23.832]   run `npm fund` for details
[07:30:23.882] Detected Next.js version: 14.2.23
[07:30:23.888] Running "npm run build"
[07:30:24.005] 
[07:30:24.005] > build
[07:30:24.006] > next build
[07:30:24.006] 
[07:30:24.582] Attention: Next.js now collects completely anonymous telemetry regarding usage.
[07:30:24.584] This information is used to shape Next.js' roadmap and prioritize features.
[07:30:24.584] You can learn more, including how to opt-out if you'd not like to participate in this anonymous program, by visiting the following URL:
[07:30:24.584] https://nextjs.org/telemetry
[07:30:24.584] 
[07:30:24.641]   ▲ Next.js 14.2.23
[07:30:24.642] 
[07:30:24.714]    Creating an optimized production build ...
[07:30:48.438] <w> [webpack.cache.PackFileCacheStrategy] Serializing big strings (153kiB) impacts deserialization performance (consider using Buffer instead and decode when needed)
[07:30:48.439] <w> [webpack.cache.PackFileCacheStrategy] Serializing big strings (153kiB) impacts deserialization performance (consider using Buffer instead and decode when needed)
[07:30:48.440] <w> [webpack.cache.PackFileCacheStrategy] Serializing big strings (153kiB) impacts deserialization performance (consider using Buffer instead and decode when needed)
[07:30:48.441] <w> [webpack.cache.PackFileCacheStrategy] Serializing big strings (153kiB) impacts deserialization performance (consider using Buffer instead and decode when needed)
[07:30:48.441] <w> [webpack.cache.PackFileCacheStrategy] Serializing big strings (153kiB) impacts deserialization performance (consider using Buffer instead and decode when needed)
[07:30:48.441] <w> [webpack.cache.PackFileCacheStrategy] Serializing big strings (153kiB) impacts deserialization performance (consider using Buffer instead and decode when needed)
[07:30:48.442] <w> [webpack.cache.PackFileCacheStrategy] Serializing big strings (153kiB) impacts deserialization performance (consider using Buffer instead and decode when needed)
[07:30:48.442] <w> [webpack.cache.PackFileCacheStrategy] Serializing big strings (153kiB) impacts deserialization performance (consider using Buffer instead and decode when needed)
[07:30:48.442] <w> [webpack.cache.PackFileCacheStrategy] Serializing big strings (152kiB) impacts deserialization performance (consider using Buffer instead and decode when needed)
[07:30:48.442] <w> [webpack.cache.PackFileCacheStrategy] Serializing big strings (153kiB) impacts deserialization performance (consider using Buffer instead and decode when needed)
[07:30:48.443] <w> [webpack.cache.PackFileCacheStrategy] Serializing big strings (152kiB) impacts deserialization performance (consider using Buffer instead and decode when needed)
[07:30:48.443] <w> [webpack.cache.PackFileCacheStrategy] Serializing big strings (152kiB) impacts deserialization performance (consider using Buffer instead and decode when needed)
[07:30:48.446] <w> [webpack.cache.PackFileCacheStrategy] Serializing big strings (153kiB) impacts deserialization performance (consider using Buffer instead and decode when needed)
[07:30:48.446] <w> [webpack.cache.PackFileCacheStrategy] Serializing big strings (153kiB) impacts deserialization performance (consider using Buffer instead and decode when needed)
[07:30:48.446] <w> [webpack.cache.PackFileCacheStrategy] Serializing big strings (153kiB) impacts deserialization performance (consider using Buffer instead and decode when needed)
[07:30:48.446] <w> [webpack.cache.PackFileCacheStrategy] Serializing big strings (153kiB) impacts deserialization performance (consider using Buffer instead and decode when needed)
[07:30:48.556] <w> [webpack.cache.PackFileCacheStrategy] Serializing big strings (153kiB) impacts deserialization performance (consider using Buffer instead and decode when needed)
[07:30:48.556] <w> [webpack.cache.PackFileCacheStrategy] Serializing big strings (153kiB) impacts deserialization performance (consider using Buffer instead and decode when needed)
[07:30:48.557] <w> [webpack.cache.PackFileCacheStrategy] Serializing big strings (153kiB) impacts deserialization performance (consider using Buffer instead and decode when needed)
[07:30:48.557] <w> [webpack.cache.PackFileCacheStrategy] Serializing big strings (153kiB) impacts deserialization performance (consider using Buffer instead and decode when needed)
[07:30:48.562] <w> [webpack.cache.PackFileCacheStrategy] Serializing big strings (153kiB) impacts deserialization performance (consider using Buffer instead and decode when needed)
[07:30:48.562] <w> [webpack.cache.PackFileCacheStrategy] Serializing big strings (153kiB) impacts deserialization performance (consider using Buffer instead and decode when needed)
[07:30:48.562] <w> [webpack.cache.PackFileCacheStrategy] Serializing big strings (153kiB) impacts deserialization performance (consider using Buffer instead and decode when needed)
[07:30:48.563] <w> [webpack.cache.PackFileCacheStrategy] Serializing big strings (153kiB) impacts deserialization performance (consider using Buffer instead and decode when needed)
[07:30:48.563] <w> [webpack.cache.PackFileCacheStrategy] Serializing big strings (152kiB) impacts deserialization performance (consider using Buffer instead and decode when needed)
[07:30:48.563] <w> [webpack.cache.PackFileCacheStrategy] Serializing big strings (153kiB) impacts deserialization performance (consider using Buffer instead and decode when needed)
[07:30:48.563] <w> [webpack.cache.PackFileCacheStrategy] Serializing big strings (152kiB) impacts deserialization performance (consider using Buffer instead and decode when needed)
[07:30:48.563] <w> [webpack.cache.PackFileCacheStrategy] Serializing big strings (152kiB) impacts deserialization performance (consider using Buffer instead and decode when needed)
[07:30:48.618] <w> [webpack.cache.PackFileCacheStrategy] Serializing big strings (153kiB) impacts deserialization performance (consider using Buffer instead and decode when needed)
[07:30:48.618] <w> [webpack.cache.PackFileCacheStrategy] Serializing big strings (153kiB) impacts deserialization performance (consider using Buffer instead and decode when needed)
[07:30:48.618] <w> [webpack.cache.PackFileCacheStrategy] Serializing big strings (153kiB) impacts deserialization performance (consider using Buffer instead and decode when needed)
[07:30:48.619] <w> [webpack.cache.PackFileCacheStrategy] Serializing big strings (153kiB) impacts deserialization performance (consider using Buffer instead and decode when needed)
[07:31:12.218] <w> [webpack.cache.PackFileCacheStrategy] Serializing big strings (153kiB) impacts deserialization performance (consider using Buffer instead and decode when needed)
[07:31:12.218] <w> [webpack.cache.PackFileCacheStrategy] Serializing big strings (153kiB) impacts deserialization performance (consider using Buffer instead and decode when needed)
[07:31:12.218] <w> [webpack.cache.PackFileCacheStrategy] Serializing big strings (153kiB) impacts deserialization performance (consider using Buffer instead and decode when needed)
[07:31:12.218] <w> [webpack.cache.PackFileCacheStrategy] Serializing big strings (153kiB) impacts deserialization performance (consider using Buffer instead and decode when needed)
[07:31:12.226] <w> [webpack.cache.PackFileCacheStrategy] Serializing big strings (153kiB) impacts deserialization performance (consider using Buffer instead and decode when needed)
[07:31:12.227] <w> [webpack.cache.PackFileCacheStrategy] Serializing big strings (153kiB) impacts deserialization performance (consider using Buffer instead and decode when needed)
[07:31:12.227] <w> [webpack.cache.PackFileCacheStrategy] Serializing big strings (153kiB) impacts deserialization performance (consider using Buffer instead and decode when needed)
[07:31:12.227] <w> [webpack.cache.PackFileCacheStrategy] Serializing big strings (153kiB) impacts deserialization performance (consider using Buffer instead and decode when needed)
[07:31:12.300] <w> [webpack.cache.PackFileCacheStrategy] Serializing big strings (153kiB) impacts deserialization performance (consider using Buffer instead and decode when needed)
[07:31:12.301] <w> [webpack.cache.PackFileCacheStrategy] Serializing big strings (153kiB) impacts deserialization performance (consider using Buffer instead and decode when needed)
[07:31:12.302] <w> [webpack.cache.PackFileCacheStrategy] Serializing big strings (153kiB) impacts deserialization performance (consider using Buffer instead and decode when needed)
[07:31:12.302] <w> [webpack.cache.PackFileCacheStrategy] Serializing big strings (153kiB) impacts deserialization performance (consider using Buffer instead and decode when needed)
[07:31:12.318] <w> [webpack.cache.PackFileCacheStrategy] Serializing big strings (153kiB) impacts deserialization performance (consider using Buffer instead and decode when needed)
[07:31:12.320] <w> [webpack.cache.PackFileCacheStrategy] Serializing big strings (153kiB) impacts deserialization performance (consider using Buffer instead and decode when needed)
[07:31:12.320] <w> [webpack.cache.PackFileCacheStrategy] Serializing big strings (153kiB) impacts deserialization performance (consider using Buffer instead and decode when needed)
[07:31:12.321] <w> [webpack.cache.PackFileCacheStrategy] Serializing big strings (153kiB) impacts deserialization performance (consider using Buffer instead and decode when needed)
[07:31:14.175]  ✓ Compiled successfully
[07:31:14.177]    Linting and checking validity of types ...
[07:31:27.691] Failed to compile.
[07:31:27.692] 
[07:31:27.692] ./src/components/blog/BlogEditor.tsx:258:22
[07:31:27.692] Type error: Cannot find name 'Tag'. Did you mean 'tag'?
[07:31:27.692] 
[07:31:27.692] [0m [90m 256 |[39m                 [33m<[39m[33mHStack[39m gap[33m=[39m{[35m2[39m} mb[33m=[39m{[35m8[39m} flexWrap[33m=[39m[32m"wrap"[39m[33m>[39m[0m
[07:31:27.692] [0m [90m 257 |[39m                   {tags[33m.[39mmap((tag[33m,[39m index) [33m=>[39m ([0m
[07:31:27.692] [0m[31m[1m>[22m[39m[90m 258 |[39m                     [33m<[39m[33mTag[39m[0m
[07:31:27.693] [0m [90m     |[39m                      [31m[1m^[22m[39m[0m
[07:31:27.693] [0m [90m 259 |[39m                       key[33m=[39m{index}[0m
[07:31:27.693] [0m [90m 260 |[39m                       size[33m=[39m[32m"md"[39m[0m
[07:31:27.693] [0m [90m 261 |[39m                       borderRadius[33m=[39m[32m"full"[39m[0m
[07:31:27.724] Static worker exited with code: 1 and signal: null
[07:31:27.744] Error: Command "npm run build" exited with 1
[07:31:28.056] 
[07:31:31.285] Exiting build container